package http

import (
	"github.com/SneatX/adeptos_go/internal/config"
	"github.com/go-chi/chi/v5"
)

func RegisterRoutes(r chi.Router, container *config.Container) {
	r.Route("/api", func(r chi.Router) {
		r.Post("/leads", container.Handlers.LeadHandler.CreateLead)
		r.Get("/leads/{id}", container.Handlers.LeadHandler.GetLeadByID)
		r.Get("/reports/{id}", container.Handlers.ReportHandler.GetReportByID)
	})
}
